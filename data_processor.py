# -*- coding: utf-8 -*-
"""
数据处理模块
负责Excel文件读取、数据验证和筛选逻辑
"""

import pandas as pd
import os
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from config import APP_CONFIG, FILTER_CONFIG

class DataProcessor:
    """数据处理类，负责Excel文件读取和数据筛选"""
    
    def __init__(self):
        """初始化数据处理器"""
        self.logger = logging.getLogger(__name__)
        self.stock_codes = []
        self.filtered_stocks = []
        
        # 实时保存相关变量
        self.analysis_start_time = None
        self.realtime_save_file = None
        
    def load_excel_file(self, file_path: str) -> List[str]:
        """
        从Excel文件读取股票代码
        
        Args:
            file_path: Excel文件路径
            
        Returns:
            股票代码列表
            
        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 文件格式不支持或数据无效
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            # 检查文件扩展名
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in APP_CONFIG['excel_extensions']:
                raise ValueError(f"不支持的文件格式: {file_ext}")
            
            # 读取Excel文件
            self.logger.info(f"开始读取Excel文件: {file_path}")
            
            # 根据文件扩展名选择引擎
            if file_ext == '.xlsx':
                df = pd.read_excel(file_path, engine='openpyxl')
            else:
                df = pd.read_excel(file_path, engine='xlrd')
            
            # 检查数据是否为空
            if df.empty:
                raise ValueError("Excel文件为空")
            
            # 获取第一列的数据作为股票代码
            first_column = df.iloc[:, 0]
            
            # 清理数据：去除空值和重复值
            stock_codes = first_column.dropna().astype(str).unique().tolist()
            
            # 过滤掉明显不是股票代码的数据
            valid_stock_codes = []
            for code in stock_codes:
                code = str(code).strip()
                # 先规范化股票代码格式（主要处理4位数补零）
                normalized_code = self._normalize_stock_code(code)
                if self._is_valid_stock_code(normalized_code):
                    valid_stock_codes.append(normalized_code)
            
            if not valid_stock_codes:
                raise ValueError("未找到有效的股票代码")
            
            self.stock_codes = valid_stock_codes
            self.logger.info(f"成功读取 {len(valid_stock_codes)} 个股票代码")
            
            return valid_stock_codes
            
        except Exception as e:
            self.logger.error(f"读取Excel文件失败: {str(e)}")
            raise
    
    def _normalize_stock_code(self, code: str) -> str:
        """
        规范化股票代码格式
        主要处理4位数股票代码自动补零（如：1320 -> 001320）
        
        Args:
            code: 原始股票代码
            
        Returns:
            规范化后的股票代码
        """
        # 去除前后空格
        code = str(code).strip()
        
        # 如果是纯数字且为4位数，则在前面补00
        if code.isdigit() and len(code) == 4:
            normalized_code = '00' + code
            self.logger.info(f"4位数股票代码自动补零: {code} -> {normalized_code}")
            return normalized_code
        
        return code
    
    def _is_valid_stock_code(self, code: str) -> bool:
        """
        验证股票代码是否有效
        
        Args:
            code: 股票代码
            
        Returns:
            是否有效
        """
        # 基本验证：长度和字符类型
        if not code or len(code) < 4:
            return False
        
        # 去除前后空格
        code = code.strip()
        
        # 股票代码通常是数字或字母数字组合
        if not code.replace('.', '').replace('-', '').isalnum():
            return False
        
        # 避免明显的非股票代码（如标题行）
        invalid_keywords = ['股票', '代码', 'code', 'stock', '序号', '编号']
        if any(keyword in code.lower() for keyword in invalid_keywords):
            return False
        
        return True
    
    def filter_stocks(self, stock_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        根据多空资金数据筛选股票
        
        Args:
            stock_data: 股票数据列表，每个元素包含股票代码和三天的资金数据
            
        Returns:
            筛选后的股票数据列表
        """
        filtered_stocks = []
        
        for stock in stock_data:
            try:
                # 获取三天的资金数据
                today_value = stock.get('today_fund', 0)
                yesterday_value = stock.get('yesterday_fund', 0)
                day_before_yesterday_value = stock.get('day_before_yesterday_fund', 0)
                
                # 数据验证
                if FILTER_CONFIG['validate_data']:
                    if not self._validate_fund_values(today_value, yesterday_value, day_before_yesterday_value):
                        stock['status'] = '数据无效'
                        continue
                
                # 筛选条件：今天 > 昨天 > 前天
                if self._meets_filter_condition(today_value, yesterday_value, day_before_yesterday_value):
                    stock['status'] = '符合条件'
                    filtered_stocks.append(stock)
                    self.logger.info(f"股票 {stock['stock_code']} 符合筛选条件")
                else:
                    stock['status'] = '不符合条件'
                    
            except Exception as e:
                self.logger.error(f"处理股票 {stock.get('stock_code', 'unknown')} 时出错: {str(e)}")
                stock['status'] = f'处理错误: {str(e)}'
        
        self.filtered_stocks = filtered_stocks
        self.logger.info(f"筛选完成，共 {len(filtered_stocks)} 只股票符合条件")
        
        return filtered_stocks
    
    def _validate_fund_values(self, today: float, yesterday: float, day_before_yesterday: float) -> bool:
        """
        验证资金数据的有效性
        
        Args:
            today: 今天的资金数据
            yesterday: 昨天的资金数据
            day_before_yesterday: 前天的资金数据
            
        Returns:
            数据是否有效
        """
        min_value = FILTER_CONFIG['data_range']['min_value']
        max_value = FILTER_CONFIG['data_range']['max_value']
        
        values = [today, yesterday, day_before_yesterday]
        
        for value in values:
            # 检查是否为数字
            if not isinstance(value, (int, float)):
                return False
            
            # 检查数值范围
            if value < min_value or value > max_value:
                return False
        
        return True
    
    def _meets_filter_condition(self, today: float, yesterday: float, day_before_yesterday: float) -> bool:
        """
        检查是否满足筛选条件
        
        Args:
            today: 今天的资金数据
            yesterday: 昨天的资金数据
            day_before_yesterday: 前天的资金数据
            
        Returns:
            是否满足条件
        """
        # 筛选条件：今天 > 昨天 > 前天
        return today > yesterday > day_before_yesterday
    
    def save_all_results_to_excel(self, output_path: str, all_stock_results: List[Dict[str, Any]]) -> bool:
        """
        将所有股票分析结果保存到Excel文件（包括成功和失败的）
        
        Args:
            output_path: 输出文件路径
            all_stock_results: 所有股票的分析结果
            
        Returns:
            是否保存成功
        """
        try:
            if not all_stock_results:
                self.logger.warning("没有数据需要保存")
                return False
            
            # 创建DataFrame
            df_data = []
            success_count = 0
            failed_count = 0
            
            for stock in all_stock_results:
                status = stock.get('status', '未知状态')
                
                # 统计成功和失败数量
                if status == '分析成功':
                    success_count += 1
                else:
                    failed_count += 1
                
                # 格式化资金数据显示
                today_fund = stock.get('today_fund', 0)
                yesterday_fund = stock.get('yesterday_fund', 0)
                day_before_fund = stock.get('day_before_yesterday_fund', 0)
                
                # 判断是否符合筛选条件
                meets_condition = "是" if (today_fund > yesterday_fund > day_before_fund and status == '分析成功') else "否"
                
                df_data.append({
                    '股票代码': str(stock['stock_code']),
                    '分析状态': status,
                    '今日资金(%)': f"{today_fund:+.3f}" if today_fund != 0 else "0.000",
                    '昨日资金(%)': f"{yesterday_fund:+.3f}" if yesterday_fund != 0 else "0.000",
                    '前日资金(%)': f"{day_before_fund:+.3f}" if day_before_fund != 0 else "0.000",
                    '符合筛选条件': meets_condition,
                    '分析时间': pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')
                })
            
            df = pd.DataFrame(df_data)
            
            # 保存到Excel文件
            df.to_excel(output_path, index=False, engine='openpyxl')
            
            self.logger.info(f"完整结果已保存到: {output_path}")
            self.logger.info(f"总计 {len(all_stock_results)} 只股票，成功 {success_count} 只，失败 {failed_count} 只")
            return True
            
        except Exception as e:
            self.logger.error(f"保存完整结果失败: {str(e)}")
            return False
    
    def save_results_to_excel(self, output_path: str, filtered_stocks: List[Dict[str, Any]]) -> bool:
        """
        将筛选结果保存到Excel文件
        
        Args:
            output_path: 输出文件路径
            filtered_stocks: 筛选后的股票数据
            
        Returns:
            是否保存成功
        """
        try:
            if not filtered_stocks:
                self.logger.warning("没有数据需要保存")
                return False
            
            # 创建DataFrame
            df_data = []
            for stock in filtered_stocks:
                df_data.append({
                    '股票代码': str(stock['stock_code']),
                    '今日资金': stock.get('today_fund', 0),
                    '昨日资金': stock.get('yesterday_fund', 0),
                    '前日资金': stock.get('day_before_yesterday_fund', 0),
                    '状态': stock.get('status', ''),
                    '筛选时间': pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')
                })
            
            df = pd.DataFrame(df_data)
            
            # 保存到Excel文件
            df.to_excel(output_path, index=False, engine='openpyxl')
            
            self.logger.info(f"结果已保存到: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存结果失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息字典
        """
        total_stocks = len(self.stock_codes)
        filtered_stocks = len(self.filtered_stocks)
        
        return {
            'total_stocks': total_stocks,
            'filtered_stocks': filtered_stocks,
            'filter_rate': filtered_stocks / total_stocks * 100 if total_stocks > 0 else 0
        }
    
    def reset(self):
        """重置数据处理器状态"""
        self.stock_codes = []
        self.filtered_stocks = []
        
        # 重置实时保存相关状态
        self.analysis_start_time = None
        self.realtime_save_file = None
        
        self.logger.info("数据处理器已重置")
    
    def get_desktop_path(self) -> str:
        """
        获取桌面路径
        
        Returns:
            桌面路径
        """
        import os
        
        # Windows系统
        if os.name == 'nt':
            desktop = os.path.join(os.path.expanduser("~"), "Desktop")
            if os.path.exists(desktop):
                return desktop
            # 中文系统可能是"桌面"
            desktop_cn = os.path.join(os.path.expanduser("~"), "桌面")
            if os.path.exists(desktop_cn):
                return desktop_cn
        
        # Linux/Mac系统
        desktop = os.path.join(os.path.expanduser("~"), "Desktop")
        if os.path.exists(desktop):
            return desktop
        
        # 如果都找不到，返回用户目录
        return os.path.expanduser("~")
    
    def generate_realtime_save_filename(self, start_time: datetime) -> str:
        """
        生成实时保存的文件名（带序号处理重复）
        
        Args:
            start_time: 分析开始时间
            
        Returns:
            文件名（不包含路径）
        """
        # 格式化时间戳
        time_str = start_time.strftime('%Y-%m-%d_%H-%M-%S')
        base_filename = f"股票分析结果_{time_str}"
        
        desktop_path = self.get_desktop_path()
        
        # 检查文件是否存在，如果存在则添加序号
        sequence = 1
        while True:
            if sequence == 1:
                filename = f"{base_filename}.xlsx"
            else:
                filename = f"{base_filename}_{sequence:03d}.xlsx"
            
            full_path = os.path.join(desktop_path, filename)
            if not os.path.exists(full_path):
                return filename
            sequence += 1
            
            # 防止无限循环
            if sequence > 999:
                filename = f"{base_filename}_{datetime.now().microsecond}.xlsx"
                break
        
        return filename
    
    def init_realtime_save(self) -> bool:
        """
        初始化实时保存功能
        
        Returns:
            是否初始化成功
        """
        try:
            # 设置分析开始时间
            self.analysis_start_time = datetime.now()
            
            # 生成文件名
            filename = self.generate_realtime_save_filename(self.analysis_start_time)
            desktop_path = self.get_desktop_path()
            self.realtime_save_file = os.path.join(desktop_path, filename)
            
            self.logger.info(f"实时保存已初始化: {self.realtime_save_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"初始化实时保存失败: {str(e)}")
            return False
    
    def save_qualified_stock_realtime(self, stock_data: Dict[str, Any]) -> bool:
        """
        实时保存单个符合条件的股票到Excel文件
        
        Args:
            stock_data: 股票数据字典
            
        Returns:
            是否保存成功
        """
        try:
            # 检查是否已初始化实时保存
            if not self.realtime_save_file:
                if not self.init_realtime_save():
                    return False
            
            # 检查股票是否符合条件
            today_fund = stock_data.get('today_fund', 0)
            yesterday_fund = stock_data.get('yesterday_fund', 0)
            day_before_fund = stock_data.get('day_before_yesterday_fund', 0)
            
            if not self._meets_filter_condition(today_fund, yesterday_fund, day_before_fund):
                return False  # 不符合条件，不保存
            
            # 准备要保存的数据
            save_data = {
                '股票代码': str(stock_data['stock_code']),
                '今日资金(%)': f"{today_fund:+.3f}",
                '昨日资金(%)': f"{yesterday_fund:+.3f}",
                '前日资金(%)': f"{day_before_fund:+.3f}",
                '状态': stock_data.get('status', ''),
                '分析时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # 检查文件是否已存在
            if os.path.exists(self.realtime_save_file):
                # 读取现有数据
                existing_df = pd.read_excel(self.realtime_save_file, engine='openpyxl')
                # 添加新数据
                new_df = pd.DataFrame([save_data])
                combined_df = pd.concat([existing_df, new_df], ignore_index=True)
            else:
                # 创建新文件
                combined_df = pd.DataFrame([save_data])
            
            # 保存到Excel文件
            combined_df.to_excel(self.realtime_save_file, index=False, engine='openpyxl')
            
            self.logger.info(f"符合条件的股票 {stock_data['stock_code']} 已实时保存到: {self.realtime_save_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"实时保存股票 {stock_data.get('stock_code', 'unknown')} 失败: {str(e)}")
            return False
    
    def generate_failed_stocks_filename(self) -> str:
        """
        生成失败股票记录的文件名
        
        Returns:
            文件名（不包含路径）
        """
        # 格式化时间戳
        time_str = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
        base_filename = f"识别失败股票_{time_str}"
        
        desktop_path = self.get_desktop_path()
        
        # 检查文件是否存在，如果存在则添加序号
        sequence = 1
        while True:
            if sequence == 1:
                filename = f"{base_filename}.xlsx"
            else:
                filename = f"{base_filename}({sequence}).xlsx"
            
            full_path = os.path.join(desktop_path, filename)
            if not os.path.exists(full_path):
                return full_path
            
            sequence += 1
            if sequence > 100:  # 防止无限循环
                return os.path.join(desktop_path, f"{base_filename}_final.xlsx")
    
    def export_failed_stocks(self, failed_list: List[Dict[str, Any]]) -> Optional[str]:
        """
        将失败股票列表导出到Excel文件（一次性写入）
        
        Args:
            failed_list: 失败股票列表，每个元素包含 {'stock_code': str, 'reason': str}
            
        Returns:
            生成的文件路径，失败时返回None
        """
        try:
            if not failed_list:
                self.logger.warning("没有失败股票需要导出")
                return None
            
            # 生成文件路径
            output_path = self.generate_failed_stocks_filename()
            
            # 创建DataFrame
            df_data = []
            for i, failed_stock in enumerate(failed_list, 1):
                df_data.append({
                    '序号': i,
                    '股票代码': str(failed_stock['stock_code']),
                    '失败原因': failed_stock['reason'],
                    '记录时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
            
            df = pd.DataFrame(df_data)
            
            # 保存到Excel文件
            df.to_excel(output_path, index=False, engine='openpyxl')
            
            self.logger.info(f"失败股票记录已导出到: {output_path}")
            self.logger.info(f"共记录 {len(failed_list)} 只失败股票")
            return output_path
            
        except Exception as e:
            self.logger.error(f"导出失败股票记录失败: {str(e)}")
            return None
    
    def append_failed_stock(self, stock_code: str, reason: str, file_path: str = None) -> bool:
        """
        实时追加单个失败股票到Excel文件
        
        Args:
            stock_code: 股票代码
            reason: 失败原因
            file_path: 目标文件路径，如果为None则自动生成
            
        Returns:
            是否追加成功
        """
        try:
            # 如果没有指定文件路径，则生成一个
            if not file_path:
                file_path = self.generate_failed_stocks_filename()
            
            # 准备要保存的数据
            failed_data = {
                '股票代码': str(stock_code),
                '失败原因': reason,
                '记录时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # 检查文件是否已存在
            if os.path.exists(file_path):
                # 读取现有数据
                existing_df = pd.read_excel(file_path, engine='openpyxl')
                # 计算新的序号
                next_sequence = len(existing_df) + 1
                failed_data['序号'] = next_sequence
                
                # 添加新数据
                new_df = pd.DataFrame([failed_data])
                combined_df = pd.concat([existing_df, new_df], ignore_index=True)
            else:
                # 创建新文件
                failed_data['序号'] = 1
                combined_df = pd.DataFrame([failed_data])
            
            # 保存到Excel文件
            combined_df.to_excel(file_path, index=False, engine='openpyxl')
            
            self.logger.info(f"失败股票 {stock_code} 已追加到: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"追加失败股票 {stock_code} 失败: {str(e)}")
            return False

# 工具函数
def fix_percentage_ocr_errors(text: str) -> str:
    """
    修正常见的百分数OCR识别错误 - 改进版
    增加上下文感知，避免过度修正

    Args:
        text: OCR识别的原始文本

    Returns:
        修正后的文本
    """
    import re

    try:
        # 去除空格和常见干扰字符
        clean_text = text.strip().replace(' ', '').replace('\n', '').replace('\t', '')

        # 如果已经包含百分号，只做基本清理
        if '%' in clean_text or '％' in clean_text:
            corrections = [
                (r'％', '%'),                        # 全角百分号 -> 半角百分号
                (r'[,，。·]', '.'),                  # 修正小数点
                (r'[－−—–]', '-'),                   # 修正负号
                (r'[＋]', '+'),                      # 修正正号
                (r'[^\d\.\-\+%]', ''),              # 清理多余字符
            ]

            corrected = clean_text
            for pattern, replacement in corrections:
                corrected = re.sub(pattern, replacement, corrected)
            return corrected

        # 对于没有百分号的文本，进行智能判断
        # 只有在特定条件下才添加百分号

        # 检查是否明显是百分号被误识别的情况
        obvious_percent_patterns = [
            r'(\d+\.?\d*)[oO°◦⁰]$',              # 以o、O、度符号结尾
            r'(\d+\.?\d*)9$',                     # 以9结尾且数值在合理范围内
            r'(\d+\.?\d*)[89]$',                  # 以8或9结尾且数值很小
        ]

        # 先进行基本字符修正
        basic_corrections = [
            (r'[,，。·]', '.'),                  # 修正小数点
            (r'[－−—–]', '-'),                   # 修正负号
            (r'[＋]', '+'),                      # 修正正号
            (r'[^\d\.\-\+oO°◦⁰89]', ''),        # 保留可能的百分号字符
        ]

        corrected = clean_text
        for pattern, replacement in basic_corrections:
            corrected = re.sub(pattern, replacement, corrected)

        # 智能百分号恢复
        # 只有在数值在合理的百分数范围内才添加百分号
        for pattern in obvious_percent_patterns:
            match = re.match(pattern, corrected)
            if match:
                number_part = match.group(1)
                try:
                    value = float(number_part)
                    # 只有在合理的百分数范围内才添加百分号
                    if 0.001 <= abs(value) <= 50.0:  # 缩小范围，避免误判
                        corrected = number_part + '%'
                        break
                except ValueError:
                    continue

        # 最终清理
        corrected = re.sub(r'[oO°◦⁰]$', '%', corrected)  # 处理剩余的百分号字符

        return corrected

    except Exception:
        return text


def detect_missing_negative_sign(text: str, context_values: list = None) -> str:
    """
    检测并恢复可能丢失的负号

    Args:
        text: OCR识别的文本
        context_values: 上下文中的其他数值，用于判断

    Returns:
        可能添加负号的文本
    """
    import re

    try:
        # 如果已经有负号，直接返回
        if text.startswith('-'):
            return text

        # 提取数值
        match = re.search(r'(\d+\.?\d*)', text)
        if not match:
            return text

        value = float(match.group(1))

        # 基于上下文判断是否应该是负数
        if context_values:
            # 如果上下文中有负数，且当前值在相似范围内，可能是负数
            negative_values = [v for v in context_values if v < 0]
            if negative_values:
                avg_negative = sum(abs(v) for v in negative_values) / len(negative_values)
                if 0.5 * avg_negative <= value <= 2.0 * avg_negative:
                    return '-' + text

        # 基于数值特征判断
        # 如果是很小的正数，可能原本是负数
        if 0.001 <= value <= 2.0:
            # 这里可以添加更多启发式规则
            pass

        return text

    except Exception:
        return text


def parse_fund_value(value_str: str, context_values: list = None) -> float:
    """
    解析资金数值字符串，支持百分数格式和资金单位格式
    包含改进的OCR错误修正功能，增加上下文感知

    Args:
        value_str: 资金数值字符串 (如: "+1.338%", "-2.025%", "123.45万", "0.1623")
        context_values: 上下文中的其他数值，用于辅助判断

    Returns:
        解析后的数值，百分数格式输出(如: 1.338, -2.025)
    """
    try:
        # 先进行OCR错误修正
        corrected_str = fix_percentage_ocr_errors(str(value_str))

        # 检测可能丢失的负号
        corrected_str = detect_missing_negative_sign(corrected_str, context_values)

        # 去除空格和特殊字符
        clean_str = corrected_str.strip().replace(',', '').replace('，', '')

        # 处理百分数格式 (+/-x.xxx%)
        if '%' in clean_str:
            # 移除百分号
            clean_str = clean_str.replace('%', '')

            # 处理正负号
            if clean_str.startswith('+'):
                # 移除正号，返回正数
                clean_str = clean_str[1:]
            # 负号保留，不需要特殊处理

            # 提取数字部分
            import re
            numbers = re.findall(r'[-]?\d*\.?\d+', clean_str)
            if numbers:
                result = float(numbers[0])
                # 检查小数精度：如果有四位小数，截取为三位
                if '.' in str(result):
                    decimal_part = str(result).split('.')[1]
                    if len(decimal_part) > 3:
                        result = round(result, 3)
                return result

            return 0.0

        # 处理中文单位格式 (123.45万, 12.34亿)
        if '万' in clean_str:
            clean_str = clean_str.replace('万', '')
            multiplier = 10000
        elif '亿' in clean_str:
            clean_str = clean_str.replace('亿', '')
            multiplier = 100000000
        else:
            multiplier = 1

        # 提取数字部分
        import re
        numbers = re.findall(r'[-+]?\d*\.?\d+', clean_str)
        if numbers:
            raw_value = float(numbers[0]) * multiplier

            # 改进的数值范围判断
            if multiplier == 1:  # 没有万、亿单位的纯数字
                # 合理的百分数范围：直接作为百分数
                if 0.001 <= abs(raw_value) <= 50.0:  # 缩小范围，更保守
                    # 检查小数精度：如果有四位小数，截取为三位
                    if '.' in str(raw_value):
                        decimal_part = str(raw_value).split('.')[1]
                        if len(decimal_part) > 3:
                            raw_value = round(raw_value, 3)
                    return raw_value

                # 大数值错误修正：更智能的转换策略
                elif abs(raw_value) >= 50:
                    return smart_value_conversion(raw_value, str(value_str), context_values)

            return raw_value

        return 0.0

    except Exception:
        return 0.0


def smart_value_conversion(raw_value: float, original_text: str, context_values: list = None) -> float:
    """
    智能数值转换，处理严重的OCR错误

    Args:
        raw_value: 原始数值
        original_text: 原始文本
        context_values: 上下文数值

    Returns:
        转换后的数值
    """
    try:
        # 分析原始文本特征
        text_length = len(original_text.replace('.', '').replace('-', ''))
        has_decimal = '.' in original_text

        # 基于上下文的智能转换
        if context_values:
            context_range = [abs(v) for v in context_values if v != 0]
            if context_range:
                avg_context = sum(context_range) / len(context_range)
                max_context = max(context_range)

                # 尝试不同的转换比例，选择最接近上下文的
                conversion_factors = [100, 1000, 10000, 100000]
                best_conversion = raw_value
                best_score = float('inf')

                for factor in conversion_factors:
                    converted = abs(raw_value) / factor
                    if 0.001 <= converted <= 50.0:  # 在合理范围内
                        # 计算与上下文的相似度
                        score = min(abs(converted - avg_context), abs(converted - max_context))
                        if score < best_score:
                            best_score = score
                            best_conversion = raw_value / factor if raw_value >= 0 else -converted

                if best_conversion != raw_value:
                    return best_conversion

        # 基于文本特征的转换
        if text_length >= 5:  # 长文本，可能是严重错误
            if abs(raw_value) >= 10000:
                return raw_value / 100000
            elif abs(raw_value) >= 1000:
                return raw_value / 10000
        elif text_length >= 3:  # 中等长度
            if abs(raw_value) >= 1000:
                return raw_value / 10000
            elif abs(raw_value) >= 100:
                return raw_value / 1000

        # 默认转换
        if abs(raw_value) >= 100:
            return raw_value / 100

        return raw_value

    except Exception:
        return raw_value

def validate_excel_file(file_path: str) -> Tuple[bool, str]:
    """
    验证Excel文件是否有效
    
    Args:
        file_path: 文件路径
        
    Returns:
        (是否有效, 错误信息)
    """
    try:
        if not os.path.exists(file_path):
            return False, "文件不存在"
        
        file_ext = os.path.splitext(file_path)[1].lower()
        if file_ext not in APP_CONFIG['excel_extensions']:
            return False, f"不支持的文件格式: {file_ext}"
        
        # 尝试读取文件
        if file_ext == '.xlsx':
            df = pd.read_excel(file_path, engine='openpyxl', nrows=1)
        else:
            df = pd.read_excel(file_path, engine='xlrd', nrows=1)
        
        if df.empty:
            return False, "文件为空"
        
        return True, ""
        
    except Exception as e:
        return False, f"文件读取错误: {str(e)}"

if __name__ == "__main__":
    # 测试代码
    processor = DataProcessor()
    
    print("=== OCR错误修正测试 ===")
    ocr_error_cases = [
        # 常见OCR错误示例
        "1.2239",      # 可能的1.223%误识别
        "1.338o",      # %被识别为o
        "2,025%",      # 小数点被识别为逗号
        "-1。338%",    # 小数点被识别为句号
        "+0 123%",     # 数字间有空格
        "1.5 %",       # 百分号前有空格
        "1.338％",     # 全角百分号
        "- 2.5%",      # 负号后有空格
    ]
    
    for case in ocr_error_cases:
        corrected = fix_percentage_ocr_errors(case)
        print(f"{case:12} -> {corrected}")
    
    print("\n=== parse_fund_value() 完整测试 ===")
    # 测试解析函数 - 包含百分数格式和OCR错误修正
    test_values = [
        # OCR错误修正测试
        "1.2239",      # 疑似1.223%
        "1.338o",      # %被识别为o -> 1.338%
        "-2,025%",     # 逗号小数点
        
        # 用户提到的实际OCR输出测试
        "0.1623",      # 应转换为 0.1623
        "202453.0",    # 应转换为 2.02453
        "52.0",        # 应转换为 0.052
        
        # 正常百分数格式测试
        "+1.338%", "-2.025%", "1.338%", "-0.5%", "+0.123%",
        
        # 原有格式测试
        "123.45", "1,234.56万", "12.34亿", "无效数据"
    ]
    
    for value in test_values:
        parsed = parse_fund_value(value)
        print(f"{value:12} -> {parsed}")
    
    print("\n=== 期望的百分数转换结果验证 ===")
    expected_results = {
        "+1.338%": 1.338,
        "-2.025%": -2.025,
        "1.338%": 1.338,
        "1.338o": 1.338,      # OCR错误修正：o -> %
        "-2,025%": -2.025,    # OCR错误修正：逗号 -> 小数点
        "0.1623": 0.1623,     # 小数值直接使用
        "202453.0": 2.02453,  # 大数值转换：除以100000
        "52.0": 0.052,        # 中等数值转换：除以1000
    }
    
    all_passed = True
    for input_val, expected in expected_results.items():
        actual = parse_fund_value(input_val)
        status = "✓ PASS" if abs(actual - expected) < 0.0001 else "✗ FAIL"
        print(f"{input_val:10} -> {actual:8.3f} (期望: {expected:8.3f}) {status}")
        if abs(actual - expected) >= 0.0001:
            all_passed = False
    
    print(f"\n=== 测试结果: {'全部通过' if all_passed else '有失败项'} ===")