# 更新日志

当前版本：**v2025.08.04.1300**

## [v2025.08.04.1300] - 2025-08-04

### 重要修复
- Ctrl + Alt + S 一键安全停止信号监控 & 调试目录惰性创建
- 新增OCR调试功能


## [v2025.08.03.1600] - 2025-08-03

### 重要修复
- 🐛 修复"停止分析"功能变量作用域错误，解决UnboundLocalError异常问题
- 🔧 移除gui_analysis.py中重复的import os语句，保持代码清洁性
- ✅ 提升股票分析流程稳定性，确保停止操作正常执行
- 🐛 **修复空仓信号OCR识别失败问题**：解决@1.png和@2.png等空仓信号无法识别的核心Bug
- 🔧 **优化信号识别模糊匹配逻辑**：允许单字符识别，解决OCR要求同时包含"空"+"仓"才能识别的限制

### 功能增强
- 📁 新增失败股票记录系统：支持Excel导出功能，实时追加失败股票记录到桌面文件
- 📊 完善钉钉通知器日志：新增异步Excel写入功能，完整记录通知历史，包括启动写入线程、记录通知到队列、手动刷写日志等
- 🔔 优化信号监控完整性：买入监控时首轮清仓信号也发送钉钉消息，确保重要信号不遗漏
- 🎯 **大幅扩展OCR错误映射表**：四种信号映射数量均衡化，空字13个、持字11个、开字10个、清字11个、仓字7个映射
- 📊 **实现渐进式信号识别策略**：精确匹配→单字符匹配→双字符组合匹配的三层识别逻辑
- 🔍 **增强OCR识别调试能力**：新增详细的识别过程日志，便于问题诊断和性能监控

### 性能优化
- ⚡ 提升鼠标操作准确性：新增0.5秒等待时间，确保鼠标操作精确度和稳定性
- 🎯 优化识别成功率：调优配置参数，提升信号识别准确性和系统可靠性
- 🎨 改进GUI用户体验：调整信号测试和监控按钮位置，优化界面布局设计

### 技术改进
- 重构失败股票处理逻辑：在data_processor.py中新增120行代码，实现完整的失败记录管理
- 增强钉钉通知器功能：在dingtalk_notifier.py中新增243行代码，实现异步日志记录和Excel写入
- 优化信号监控架构：完善unified_signal_monitor.py，确保清仓信号的完整通知覆盖
- 提升代码质量：移除重复导入，修复变量作用域问题，增强代码稳定性
- **重构enhanced_signal_analyzer.py**：扩展clean_ocr_text()方法，新增40+个OCR错误映射，覆盖常见误识别情况
- **优化parse_signal_content()方法**：实现单字符优先识别逻辑，空仓信号识别成功率显著提升
- **改进_perform_ocr()方法**：添加详细的识别过程日志，包括图像处理、引擎调用、结果解析全流程
- **完善错误处理机制**：温和的文字清洗策略，保留有用字符，避免过度清洗导致信息丢失

---

## [v2025.08.03.1500] - 2025-08-03

### 技术改进
- 📊 完成买卖信号检测延时深度分析：详细分析从search_stock到鼠标移动的完整延时链路
- ⚡ 识别性能优化机会：发现页面切换等待时间(page_switch_wait)为0秒可能影响识别准确性  
- 🔧 延时配置优化建议：建议将page_switch_wait从0秒调整为0.3-0.5秒提升稳定性
- 📈 系统响应分析：分析股票间0.1秒处理间隔对系统性能的影响

### 性能分析成果
- 🔍 映射完整的信号检测延时处理流程：search_stock → 页面稳定等待 → 鼠标重置 → OCR识别
- 📋 识别关键延时配置项：page_load_wait(0.5s)、page_switch_wait(0s)、股票间隔(0.1s)、鼠标移动(0s)
- 🎯 提供基于性能与准确性平衡的优化建议：在保持效率的同时提高信号检测可靠性
- 📊 为后续性能调优提供数据支撑：建立延时配置与识别准确性的关联分析基础

---

## [v2025.08.03.1400] - 2025-08-03

### 重要修复
- 🐛 修复信号测试功能日志输出缺失问题：解决"基础测试"和"增强测试"只显示开始记录就无后续信息的关键Bug
- 🔧 修复GUI消息处理循环：添加对`signal_test_result`和`signal_test_error`消息类型的正确处理路由
- 📡 修复测试日志传递断开：enhanced_signal_analyzer.py中的日志现在正确传递到GUI消息队列
- ⚠️ 增强异常处理机制：特别处理依赖库缺失问题（cv2, mss, PIL等），提供详细错误诊断和解决建议

### 功能增强
- 🎨 美化测试日志显示：使用emoji图标优化测试过程的可视化反馈，包括📋开始、📸截图、🔍OCR、🎯解析等
- 📊 详细测试进度追踪：基础测试显示完整OCR处理流程，增强测试显示颜色识别+OCR的详细诊断过程
- 💾 调试功能完善：增强测试自动保存调试截图，便于问题诊断和结果验证
- 🚀 实时测试反馈：每个测试步骤都有即时的GUI日志反馈，用户可清晰了解测试执行状态

### 技术改进
- 重构enhanced_signal_analyzer.py：新增`_log_message()`统一日志处理方法，支持同时输出到logger和GUI
- 升级gui_signal_operations.py：信号分析器创建时传递消息队列参数，确保GUI日志连接
- 优化gui_handlers.py：完善消息处理循环，确保新测试消息类型能被正确处理
- 改进错误处理逻辑：区分导入错误、系统错误等不同异常类型，提供针对性解决方案

### 用户体验提升
- ✅ 测试结果可见性：彻底解决测试成功时无输出、失败时显示旧警告的问题
- 🔍 详细诊断信息：基础测试展示OCR识别全流程，增强测试提供颜色+OCR双重诊断
- 📱 智能错误提示：缺少依赖库时显示具体安装命令，便于用户快速解决问题
- 🎯 过程透明化：从截图到最终结果的每个步骤都有清晰的进度反馈

---

## [v2025.08.02.1600] - 2025-08-02

### 新增功能
- 🚀 颜色识别+OCR混合信号分析系统：集成增强版信号分析器，支持超快颜色识别
- 🔄 智能信号识别回退机制：颜色识别失败时自动切换到OCR识别，确保识别稳定性
- 🎯 四种信号类型优化识别：针对持仓、开仓、空仓、清仓信号的专门颜色范围优化
- 📊 性能统计监控功能：实时统计颜色识别和OCR识别的成功率、执行时间和回退次数

### 性能优化
- ⚡ 信号识别速度提升200-700倍：相比纯OCR方案，颜色识别仅需0.3ms完成
- 🎨 空仓信号识别准确率显著提升：通过颜色范围[0,0,109]-[61,155,222]直接识别灰色空仓信号
- ❌ "OCR识别无结果"警告大幅减少：混合模式双重保障确保识别成功率
- 🔧 系统资源占用显著降低：颜色识别减少CPU和内存使用

### 技术改进
- 升级gui_signal_operations.py：从原始SignalAnalyzer升级到EnhancedSignalAnalyzer
- 增强配置管理：支持混合识别模式的完整配置参数传递（APP_CONFIG）
- 优化导入结构：统一使用enhanced_signal_analyzer，保持向后兼容性
- 改进错误处理：颜色+OCR双重保障机制，提升信号监控稳定性
- 完善统计体系：新增color_success、color_fallback、ocr_success等详细统计指标

### 用户体验提升
- 🚀 无需用户干预：系统自动启用混合识别模式，智能选择最优识别策略
- 📈 识别成功率大幅提升：特别解决了空仓信号识别困难的问题
- ⚙️ 配置化管理：支持通过config.py调整识别模式（color/ocr/hybrid）
- 🔍 实时反馈：详细的识别过程日志，便于性能监控和问题诊断

---

## [v2025.08.01.1805] - 2025-08-01

### 新增功能
- 新增GUI截图操作模块 (gui_screenshot_operations.py)
- 支持截取多空资金区域和信号区域并保存为PNG文件
- 提供截图保存功能，使用与OCR相同的截图方法
- 支持时间戳命名和多区域截图保存

### 性能优化
- 优化鼠标移动操作，减少延迟时间
- 将鼠标移动的持续时间从0.2秒调整为0秒，提高响应速度
- 将信号监控中的短暂延迟从0.5秒减少至0.1秒，提升整体性能

### 技术改进
- 更新多个GUI模块和核心组件，包括信号操作、分析、设置等模块
- 改进信号监控系统的响应速度和稳定性
- 优化Web自动化相关功能
- 增强截图处理和图像处理能力
- 完善错误处理和状态管理机制

---

## [v2025.08.01.1000] - 2025-08-01

### 修复
- 修复鼠标定位功能在start_analysis时不工作的关键问题
- 修复CompassAutomator实例初始化时缺少鼠标重置回调函数的问题
- 改进连接检查逻辑，确保在继续分析和全新分析时回调函数都能正确设置
- 解决了鼠标定位功能启用但不执行移动操作的问题

### 技术改进
- 在gui_ocr_operations.py的init_compass_early方法中添加mouse_reset_callback参数
- 在gui_analysis.py中增强连接检查逻辑，自动验证和设置缺失的回调函数
- 确保compass_automator.py中的鼠标重置逻辑能够正常调用回调函数

---

## [v2025.08.01.0832] - 2025-08-01

### 界面优化
- 针对1920*1080分辨率屏幕优化界面布局，确保完整显示
- 主窗口尺寸调整：1400x650 → 1200x550，减少14%占用空间
- 最小窗口尺寸：1100x450 → 1000x400，提高适配性
- 表格列宽整体缩小15%，总宽度从600px减少至505px

### 控件尺寸优化
- 日志文本框：30行→20行，50字符→45字符宽度
- 表格默认行数：14行→10行，减少垂直空间占用
- 进度条长度：150px→120px，120px→100px
- 按钮宽度统一缩小2-3字符，提高布局紧凑性
- 输入框宽度：20字符→15字符
- 网页表格列宽：120px→100px

### 布局间距精细调整
- 所有padding从10-15px统一缩减为8px
- 控件间距减少2px，优化空间利用
- 保持视觉层次清晰的同时最大化内容显示区域

### 用户体验提升
- 在1920x1080屏幕上占用约63%宽度，51%高度
- 保留足够边距确保任何分辨率下完整显示
- 保持所有功能完整性，仅优化显示效果
- 通过语法检查，确保稳定运行

---

## [v2025.07.31.1500] - 2025-07-31

### 新增功能
- 新增4位数股票代码自动补零功能，自动将4位数代码（如1320）转换为6位数（001320）
- 避免4位数股票代码被误识别为其他代码（如1320被误识别为301320）
- 新增`_normalize_stock_code()`方法，在Excel文件读取阶段自动应用股票代码规范化
- Excel文件格式标准化：移除所有保存文件中的"序号"列，简化文件结构
- 将"股票代码"调整为第一列，符合用户使用习惯和数据分析需求

### 改进
- 优化`start_stock_import_process`功能的股票代码处理逻辑
- 统一三种Excel保存方式的列结构：完整结果、实时保存、筛选结果文件
- 确保股票代码以字符串格式保存，完美保留前导零（如001320显示为6位数字）
- 提升批量股票导入的准确性和可靠性，减少用户手动干预

### 技术改进
- 在`data_processor.py`中集成股票代码规范化处理逻辑
- 重构Excel保存方法，移除序号相关的计数器代码和实例变量
- 使用`str(stock_code)`确保数据完整性，支持多种格式的代码输入
- 优化数据处理流程，提高代码可维护性，保持向后兼容性

---

## [v2025.07.30.1400] - 2025-07-30

### 文档改进
- 完善CLAUDE.md项目文档，增加批处理脚本详细说明
- 新增page_load_detector.py模块文档，完善页面加载检测功能说明
- 重新组织测试文件分类，按功能领域分组提高可读性
- 优化文档结构，为开发者提供更清晰的项目架构指导

### 技术改进
- 添加start.bat和deploy.bat脚本功能说明，详细介绍智能启动和一键部署特性
- 完善专业组件列表，确保所有核心模块都有完整文档覆盖
- 优化测试命令分类：核心功能、Web自动化、页面加载性能、专项测试
- 提升项目文档的完整性和开发者友好度

---

## [v2025.07.30.1347] - 2025-07-30

### 界面优化
- 重新设计"指南针数据分析"界面布局，显著提升用户体验
- 主窗口宽度从800px扩展至1200px，充分利用现代显示器的宽屏优势
- 将原来8个垂直堆叠的功能区域重组为5行紧凑布局，大幅减少界面高度
- 实现多列并排布局：OCR设置+系统设置、股票分析+信号监控+自选股管理

### 控件布局精细优化
- OCR区域设置：功能按钮与显示标签左右对齐，更加紧凑直观
- 信号监控模块：买入/卖出信号控件水平排列，节省垂直空间
- 自选股管理：移除不常用的暂停按钮，优化为开始添加+停止的简洁布局
- 系统设置：鼠标定位和自动停止功能并排显示，提高配置效率

### 用户体验提升
- 减少滚动操作需求，主要功能区域可在一屏内完整显示
- 保持所有现有功能完整性，仅优化UI布局不影响业务逻辑
- 按钮和控件排列更加合理，快速找到所需功能
- 整体界面更加现代化和专业化，符合当前UI设计趋势

---

## [v2025.07.30.1200] - 2025-07-30

### 新增功能
- 新增股票代码智能清理功能，自动从"300366 预ST"格式中提取6位数字代码"300366"
- 新增`extract_6digit_stock_code()`函数，支持处理ST、预ST、*ST、退市等各种股票后缀格式
- 支持多种股票代码格式的统一处理，确保Excel输出的一致性

### 改进
- 优化网页数据提取流程，在HTML解析阶段自动清理股票代码格式
- 增强HTML数据解析器(`html_data_parser.py`)，提高股票代码识别准确性
- 完善数据清洗机制，支持带空格、特殊标记的股票代码处理
- 提升用户体验，无需手动处理复杂的股票代码格式

### 技术改进
- 在`html_data_parser.py`的`_extract_row_data()`方法中集成股票代码清理逻辑
- 采用正则表达式`r'^(\d{6})'`精确匹配6位数字股票代码
- 添加备用清理策略，提高代码提取的可靠性
- 完善测试覆盖，验证12种不同股票代码格式的处理效果

---

## [v2025.07.29.1134] - 2025-07-29

### 新增功能
- 新增系统设置可视化界面，支持通过勾选框直接控制关键配置项
- 新增"启用鼠标定位功能"可视化开关，无需手动编辑配置文件
- 新增"15:01后自动停止信号检测"可视化开关，统一控制买入和卖出信号监控

### 改进
- 优化界面布局，系统设置区域采用并排布局，充分利用水平空间
- 简化字体配置，移除所有自定义font参数，使用系统默认字体
- 增强配置管理机制，支持实时配置保存和加载
- 提升用户体验，配置变化立即生效且持久化到配置文件

### 技术改进
- 新增`update_mouse_positioning_config()`和`update_auto_stop_config()`配置更新函数
- 完善GUI初始化流程，自动加载并同步配置状态
- 优化错误处理机制，配置保存失败时自动恢复界面状态

---

## [v2025.07.28.2303] - 2025-07-28

### 改进
- 优化GUI界面布局设计，提升用户体验
- 重新组织操作控制区域，分为股票分析、信号监控、自选股管理三个模块
- 统一按钮样式和宽度，界面更加整洁协调
- 改善各区域间距和padding，增加视觉呼吸感
- 优化表格显示行数和权重分配，提高空间利用率

---

## [v2025.07.28.1800] - 2025-07-28

### 新增功能
- 新增卖出信号监控系统 (SellSignalMonitor)
- 新增统一信号分析器 (SignalAnalyzer)，支持持仓、开仓、空仓、清仓四种信号
- 新增股票自动导入功能，支持批量添加自选股
- 新增基础信号监控架构 (BaseSignalMonitor)，为买入/卖出信号提供统一基础
- 新增清仓信号危险提醒功能

### 改进
- 重构买入信号监控系统，基于新的基础架构
- 优化钉钉通知器集成，改进通知逻辑
- GUI界面模块化重构，采用更清晰的Mixin模式
- 增强版本管理系统，添加版本显示功能
- 完善配置管理机制，提高系统稳定性
- 优化OCR识别和信号处理逻辑

---

## [v2025.07.28.1517] - 2025-07-28

### 新增功能
- 新增鼠标定位功能开关配置项 (enable_mouse_positioning)
- 支持在配置文件中控制是否启用鼠标定位功能
- 改进用户体验，提供更灵活的配置选项
- 向后兼容，默认启用鼠标定位功能

### 改进
- 增强了鼠标定位系统的可配置性
- 优化了配置管理机制

---

## [v2025.01.01.0000] - 2025-01-01

### 新增功能
- 支持便携式部署和一键安装
- 集成智能更新机制
- 实现买入信号发给钉钉机器人
- 添加买入信号的识别功能
- 增加鼠标坐标设置功能，添加pyautogui组件

### 改进
- 优化PaddleOCR配置和性能
- 改进用户界面和错误处理
- 优化OCR识别准确率
- 完善错误处理和用户体验

### 新增
- 创建自动化部署和更新机制
- 增加是否开启鼠标定位功能的配置

---

## 版本号格式说明

版本号格式：`vYYYY.MM.DD.HHMM`

- `YYYY`：年份
- `MM`：月份（01-12）
- `DD`：日期（01-31）
- `HHMM`：时间（24小时制，精确到分钟）

## 更新指南

1. 修改此文档顶部的当前版本号
2. 在对应版本段落添加详细的更新内容
3. 按照时间倒序排列版本记录
4. 使用标准的语义化版本标签：
   - **新增功能**：全新的功能特性
   - **改进**：对现有功能的增强
   - **修复**：错误修复
   - **变更**：可能影响兼容性的变更
   - **移除**：已移除的功能