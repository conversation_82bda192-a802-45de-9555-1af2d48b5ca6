# -*- coding: utf-8 -*-
"""
GUI界面设置模块
包含所有界面初始化和布局相关的方法
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
import logging
import sys

from config import GUI_CONFIG, APP_CONFIG
from gui_handlers import GUILogHandler
from version_utils import get_current_version


class GUISetupMixin:
    """GUI界面设置Mixin类"""
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=getattr(logging, APP_CONFIG['log_level']),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        # 获取根logger，以便捕获所有模块的日志
        self.root_logger = logging.getLogger()
        
    def setup_window(self):
        """设置主窗口"""
        config = GUI_CONFIG['main_window']
        
        # 获取当前版本号并添加到窗口标题
        try:
            version = get_current_version()
            title_with_version = f"{config['title']} {version}"
        except Exception as e:
            # 如果获取版本号失败，使用原始标题
            title_with_version = config['title']
            
        self.root.title(title_with_version)
        self.root.geometry(f"{config['width']}x{config['height']}")
        self.root.minsize(config['min_width'], config['min_height'])
        
        # 设置窗口居中
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (config['width'] // 2)
        y = (self.root.winfo_screenheight() // 2) - (config['height'] // 2)
        self.root.geometry(f"{config['width']}x{config['height']}+{x}+{y}")
        
    def setup_variables(self):
        """设置变量"""
        self.excel_path_var = tk.StringVar()
        self.status_var = tk.StringVar(value=GUI_CONFIG['status_bar']['ready_message'])
        self.progress_var = tk.DoubleVar()
        self.region_info_var = tk.StringVar(value="区域位置与尺寸")
        self.buy_signal_region_info_var = tk.StringVar(value="买入信号区域位置与尺寸")
        self.status_region_info_var = tk.StringVar(value="状态区域位置与尺寸")
        
        # 鼠标基准位置相关变量
        self.mouse_position_var = tk.StringVar(value="未设置基准位置")
        
        # 股票自动输入相关变量
        self.stock_import_status_var = tk.StringVar(value="未开始")
        self.stock_import_progress_var = tk.DoubleVar()
        
        # 截图功能相关变量
        self.screenshot_status_var = tk.StringVar(value="点击按钮进行截图")
        self.screenshots_dir = "screenshots"
        
        # 系统设置相关变量
        self.enable_mouse_positioning_var = tk.BooleanVar()
        self.auto_stop_signal_var = tk.BooleanVar()
        
        # 失败股票记录相关变量
        self.failed_stocks = []  # 存储分析失败的股票列表
        
    def setup_widgets(self):
        """设置控件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 创建标签页控件
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 设置标签页选中状态的文字颜色
        style = ttk.Style()
        style.map("TNotebook.Tab", foreground=[("selected", "blue"), ("active", "darkblue")])
        
        # 创建第一个标签页：指南针数据分析
        self.compass_frame = ttk.Frame(self.notebook, padding="8")
        self.notebook.add(self.compass_frame, text="【1】指南针数据分析")
        
        # 创建第二个标签页：网页数据获取
        self.web_frame = ttk.Frame(self.notebook, padding="8")
        self.notebook.add(self.web_frame, text="【2】网页数据获取")
        
        # 设置指南针数据分析标签页内容
        self.setup_compass_widgets()
        
        # 设置网页数据获取标签页内容
        self.setup_web_widgets()
        
        # 状态栏（共用）
        self.status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W, 
                                   padding="5")
        self.status_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        
        # 初始化日志处理器并添加到根logger以捕获所有模块的日志
        self.log_handler = GUILogHandler(self.log_text, self.message_queue)
        # 设置日志处理器格式
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        self.log_handler.setFormatter(formatter)
        # 添加到根logger以捕获所有模块的日志消息
        self.root_logger.addHandler(self.log_handler)
        # 设置根logger级别确保所有消息都能被处理
        self.root_logger.setLevel(getattr(logging, APP_CONFIG['log_level']))
        # 也添加到当前模块的logger
        self.logger.addHandler(self.log_handler)
        
    def setup_compass_widgets(self):
        """设置指南针数据分析标签页的控件"""
        
        # 第0行：文件路径区域（跨整个宽度）
        file_frame = ttk.LabelFrame(self.compass_frame, text="文件路径", padding="8")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 8))
        file_frame.grid_columnconfigure(0, weight=1)
        
        # 文件路径输入框和浏览按钮在同一行
        path_container = ttk.Frame(file_frame)
        path_container.grid(row=0, column=0, sticky=(tk.W, tk.E))
        path_container.grid_columnconfigure(0, weight=1)
        
        self.excel_entry = ttk.Entry(path_container, textvariable=self.excel_path_var)
        self.excel_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 8))
        
        ttk.Button(path_container, text="浏览", command=self.select_excel_file, width=10).grid(row=0, column=1)
        
        # 创建左右两个主要容器
        # 左侧容器：包含所有功能模块（垂直排列）
        left_frame = ttk.Frame(self.compass_frame)
        left_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        
        # 右侧容器：运行日志
        right_frame = ttk.Frame(self.compass_frame) 
        right_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))
        
        # 设置左右区域的权重比例：左侧3，右侧2
        self.compass_frame.grid_columnconfigure(0, weight=3)
        self.compass_frame.grid_columnconfigure(1, weight=2)
        self.compass_frame.grid_rowconfigure(1, weight=1)
        
        # ========== 左侧区域布局（按图片顺序垂直排列）==========
        
        # 第1行：信号监控（单行显示买入和卖出）
        signal_frame = ttk.LabelFrame(left_frame, text="信号监控", padding="8")
        signal_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 8))
        
        # 信号监控容器 - 买入和卖出在同一行
        signal_container = ttk.Frame(signal_frame)
        signal_container.grid(row=0, column=0, sticky=(tk.W, tk.E))
        signal_container.grid_columnconfigure(8, weight=1)
        
        # 买入信号控件
        ttk.Label(signal_container, text="信号测试：", width=8).grid(row=0, column=5, sticky=tk.W)
        
        self.basic_signal_test_btn = ttk.Button(signal_container, text="基础测试", command=self.test_basic_signal_recognition, width=8)
        self.basic_signal_test_btn.grid(row=0, column=6, padx=(2, 1))
        
        self.enhanced_signal_test_btn = ttk.Button(signal_container, text="增强测试", command=self.test_enhanced_signal_recognition, width=8)
        self.enhanced_signal_test_btn.grid(row=0, column=7, padx=(1, 8))
        
        # 信号监控控件
        ttk.Label(signal_container, text="信号监控：", width=8).grid(row=0, column=0, sticky=tk.W)
        
        self.buy_signal_start_btn = ttk.Button(signal_container, text="买入", command=self.start_buy_signal_monitoring, width=6)
        self.buy_signal_start_btn.grid(row=0, column=1, padx=(2, 1))
        
        self.buy_signal_stop_btn = ttk.Button(signal_container, text="停止", command=self.stop_buy_signal_monitoring, state=tk.DISABLED, width=6)
        self.buy_signal_stop_btn.grid(row=0, column=2, padx=(1, 1))
        
        self.sell_signal_start_btn = ttk.Button(signal_container, text="卖出", command=self.start_sell_signal_monitoring, width=6)
        self.sell_signal_start_btn.grid(row=0, column=3, padx=(1, 1))
        
        self.sell_signal_stop_btn = ttk.Button(signal_container, text="停止", command=self.stop_sell_signal_monitoring, state=tk.DISABLED, width=6)
        self.sell_signal_stop_btn.grid(row=0, column=4, padx=(1, 0))
        
        # 第2行：股票分析（单行显示按钮和进度条）
        analysis_frame = ttk.LabelFrame(left_frame, text="股票分析", padding="8")
        analysis_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 8))
        
        # 分析控件容器 - 按钮和进度条在同一行
        analysis_container = ttk.Frame(analysis_frame)
        analysis_container.grid(row=0, column=0, sticky=(tk.W, tk.E))
        analysis_container.grid_columnconfigure(5, weight=1)
        
        self.start_btn = ttk.Button(analysis_container, text="开始分析", command=self.start_analysis, width=7)
        self.start_btn.grid(row=0, column=0, padx=(0, 3))
        
        self.stop_btn = ttk.Button(analysis_container, text="停止分析", command=self.stop_analysis, state=tk.DISABLED, width=7)
        self.stop_btn.grid(row=0, column=1, padx=(0, 3))
        
        self.save_btn = ttk.Button(analysis_container, text="保存结果", command=self.save_results, state=tk.DISABLED, width=7)
        self.save_btn.grid(row=0, column=2, padx=(0, 8))
        
        ttk.Label(analysis_container, text="进度：").grid(row=0, column=3, sticky=tk.W, padx=(0, 5))
        self.progress_bar = ttk.Progressbar(analysis_container, variable=self.progress_var, maximum=100, length=120)
        self.progress_bar.grid(row=0, column=4, sticky=(tk.W, tk.E), padx=(0, 5))
        
        # 第3行：自选股管理（单行显示所有控件）
        stock_manage_frame = ttk.LabelFrame(left_frame, text="自选股管理", padding="8")
        stock_manage_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 8))
        
        # 自选股管理容器 - 所有控件在同一行
        stock_container = ttk.Frame(stock_manage_frame)
        stock_container.grid(row=0, column=0, sticky=(tk.W, tk.E))
        stock_container.grid_columnconfigure(5, weight=1)
        
        self.stock_import_start_btn = ttk.Button(stock_container, text="开始添加", command=self.start_stock_import_process, width=7)
        self.stock_import_start_btn.grid(row=0, column=0, padx=(0, 3))
        
        self.stock_import_stop_btn = ttk.Button(stock_container, text="停止", command=self.stop_stock_import, state=tk.DISABLED, width=6)
        self.stock_import_stop_btn.grid(row=0, column=1, padx=(0, 8))
        
        ttk.Label(stock_container, text="状态：").grid(row=0, column=2, sticky=tk.W, padx=(0, 3))
        self.stock_import_status_label = ttk.Label(stock_container, textvariable=self.stock_import_status_var, width=8)
        self.stock_import_status_label.grid(row=0, column=3, sticky=tk.W, padx=(0, 5))
        
        self.stock_import_progress_bar = ttk.Progressbar(stock_container, variable=self.stock_import_progress_var, maximum=100, length=100)
        self.stock_import_progress_bar.grid(row=0, column=4, sticky=(tk.W, tk.E), padx=(0, 5))
        
        # 第4行：OCR区域设置
        ocr_frame = ttk.LabelFrame(left_frame, text="OCR区域设置", padding="8")
        ocr_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 8))
        
        # OCR设置容器 - 采用网格布局
        ocr_container = ttk.Frame(ocr_frame)
        ocr_container.grid(row=0, column=0, sticky=(tk.W, tk.E))
        ocr_container.grid_columnconfigure(1, weight=1)
        
        # 第一行：选择屏幕区域
        ttk.Button(ocr_container, text="选择屏幕区域", command=self.select_screen_region, width=12).grid(
            row=0, column=0, sticky=tk.W, padx=(0, 10), pady=(0, 5)
        )
        self.region_label = ttk.Label(ocr_container, textvariable=self.region_info_var)
        self.region_label.grid(row=0, column=1, sticky=tk.W, pady=(0, 5))
        
        # 第二行：选择信号区域
        ttk.Button(ocr_container, text="选择信号区域", command=self.select_buy_signal_region, width=12).grid(
            row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(0, 5)
        )
        self.buy_signal_region_label = ttk.Label(ocr_container, textvariable=self.buy_signal_region_info_var)
        self.buy_signal_region_label.grid(row=1, column=1, sticky=tk.W, pady=(0, 5))
        
        # 第三行：选择状态区域
        ttk.Button(ocr_container, text="选择状态区域", command=self.select_status_region, width=12).grid(
            row=2, column=0, sticky=tk.W, padx=(0, 10), pady=(0, 5)
        )
        self.status_region_label = ttk.Label(ocr_container, textvariable=self.status_region_info_var)
        self.status_region_label.grid(row=2, column=1, sticky=tk.W, pady=(0, 5))
        
        # 第四行：获取鼠标坐标
        self.mouse_capture_btn = ttk.Button(ocr_container, text="获取鼠标坐标", command=self.start_mouse_coordinate_capture, width=12)
        self.mouse_capture_btn.grid(row=3, column=0, sticky=tk.W, padx=(0, 10), pady=(0, 5))
        self.mouse_position_label = ttk.Label(ocr_container, textvariable=self.mouse_position_var)
        self.mouse_position_label.grid(row=3, column=1, sticky=tk.W, pady=(0, 5))
        
        # 第五行：截图保存
        ttk.Button(ocr_container, text="截图保存", command=self.capture_and_save_screenshots, width=12).grid(
            row=4, column=0, sticky=tk.W, padx=(0, 10), pady=(0, 5)
        )
        self.screenshot_status_label = ttk.Label(ocr_container, textvariable=self.screenshot_status_var)
        self.screenshot_status_label.grid(row=4, column=1, sticky=tk.W, pady=(0, 5))
        
        # 第六行：多空资金OCR验证
        ttk.Button(ocr_container, text="OCR方法验证", command=self.validate_fund_ocr_methods, width=12).grid(
            row=5, column=0, sticky=tk.W, padx=(0, 10), pady=(0, 5)
        )
        self.validation_status_label = ttk.Label(ocr_container, text="验证4种OCR处理方法的识别效果")
        self.validation_status_label.grid(row=5, column=1, sticky=tk.W, pady=(0, 5))
        
        # OCR状态显示
        self.ocr_status_label = ttk.Label(ocr_frame, text="OCR状态：", foreground="gray")
        self.ocr_status_label.grid(row=1, column=0, sticky="w", pady=(5, 0))
        
        # 第5行：系统设置
        system_frame = ttk.LabelFrame(left_frame, text="系统设置", padding="8")
        system_frame.grid(row=4, column=0, sticky=(tk.W, tk.E), pady=(0, 8))
        
        # 系统设置容器
        system_container = ttk.Frame(system_frame)
        system_container.grid(row=0, column=0, sticky=(tk.W, tk.E))
        system_container.grid_columnconfigure(0, weight=1)
        system_container.grid_columnconfigure(1, weight=1)
        
        # 鼠标定位功能勾选框
        self.mouse_positioning_check = ttk.Checkbutton(
            system_container,
            text="启用鼠标定位功能",
            variable=self.enable_mouse_positioning_var,
            command=self.on_mouse_positioning_changed
        )
        self.mouse_positioning_check.grid(row=0, column=0, sticky=tk.W, padx=(0, 20), pady=(5, 5))
        
        # 自动停止信号检测勾选框
        self.auto_stop_signal_check = ttk.Checkbutton(
            system_container,
            text="15:01后自动停止信号检测",
            variable=self.auto_stop_signal_var,
            command=self.on_auto_stop_changed
        )
        self.auto_stop_signal_check.grid(row=0, column=1, sticky=tk.W, pady=(5, 5))
        
        # 第6行：筛选结果区域
        result_frame = ttk.LabelFrame(left_frame, text="筛选结果", padding="8")
        result_frame.grid(row=5, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 8))
        
        # 结果表格
        self.setup_result_table(result_frame)
        
        # 配置左侧区域的权重分配
        left_frame.grid_rowconfigure(5, weight=1)  # 筛选结果区域可扩展
        left_frame.grid_columnconfigure(0, weight=1)
        
        # ========== 右侧区域布局：运行日志 ==========
        
        log_frame = ttk.LabelFrame(right_frame, text="运行日志", padding="8")
        log_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 日志文本框 - 优化尺寸适配屏幕
        self.log_text = scrolledtext.ScrolledText(log_frame, height=20, width=45)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置右侧日志框架权重  
        right_frame.grid_rowconfigure(0, weight=1)
        right_frame.grid_columnconfigure(0, weight=1)
        log_frame.grid_rowconfigure(0, weight=1)
        log_frame.grid_columnconfigure(0, weight=1)
        
    def setup_web_widgets(self):
        """设置网页数据获取标签页的控件"""
        
        # 在网页标签页中设置网页自动化控件
        web_automation_frame = self.setup_web_automation_widgets_in_tab()
        web_automation_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(10, 10))
        
        # 网页数据获取结果显示区域
        web_result_frame = ttk.LabelFrame(self.web_frame, text="网页数据获取结果", padding="12")
        web_result_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 网页数据结果表格
        self.setup_web_result_table(web_result_frame)
        
        # 配置网页标签页布局权重
        self.web_frame.grid_rowconfigure(1, weight=1)  # 结果区域可扩展
        self.web_frame.grid_columnconfigure(0, weight=1)  # 单列可扩展
        
    def setup_web_automation_widgets_in_tab(self):
        """在网页标签页中设置网页自动化控件"""
        
        # 创建网页自动化框架
        web_automation_frame = ttk.LabelFrame(self.web_frame, text="小草选股数据获取", padding="8")
        
        # 网站URL显示
        url_frame = ttk.Frame(web_automation_frame)
        url_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(url_frame, text="目标网站:").pack(side=tk.LEFT)
        web_url_label = ttk.Label(url_frame, text="https://www.topxlc.com/ddcj-yqs-xc/web/", 
                                      foreground="blue", cursor="hand2")
        web_url_label.pack(side=tk.LEFT, padx=(5, 0))
        
        # 配置状态显示
        config_frame = ttk.Frame(web_automation_frame)
        config_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(config_frame, text="配置状态:").pack(side=tk.LEFT)
        self.config_status_label = ttk.Label(config_frame, text="未配置", foreground="red")
        self.config_status_label.pack(side=tk.LEFT, padx=(5, 0))
        
        ttk.Button(config_frame, text="配置网页元素", command=self.show_config_wizard, width=12).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(config_frame, text="仅配置数据元素", command=self.show_data_config_wizard, width=12).pack(side=tk.RIGHT)
        
        # 登录信息框架
        login_frame = ttk.LabelFrame(web_automation_frame, text="登录信息", padding="8")
        login_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 第一行：用户名和密码
        login_row1 = ttk.Frame(login_frame)
        login_row1.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(login_row1, text="用户名:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.web_username_entry = ttk.Entry(login_row1, width=15)
        self.web_username_entry.grid(row=0, column=1, sticky=tk.W, padx=(0, 10))
        
        ttk.Label(login_row1, text="密码:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        self.web_password_entry = ttk.Entry(login_row1, width=15, show="*")
        self.web_password_entry.grid(row=0, column=3, sticky=tk.W)
        
        # 第二行：保存凭据选项
        login_row2 = ttk.Frame(login_frame)
        login_row2.pack(fill=tk.X)
        
        self.save_credentials_var = tk.BooleanVar()
        save_check = ttk.Checkbutton(login_row2, text="保存登录信息", 
                                    variable=self.save_credentials_var,
                                    command=self.on_save_credentials_changed)
        save_check.pack(side=tk.LEFT)
        
        ttk.Button(login_row2, text="清除保存的信息", command=self.clear_saved_credentials, width=12).pack(side=tk.RIGHT)
        
        # 操作按钮框架
        button_frame = ttk.Frame(web_automation_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 启动浏览器按钮（默认禁用，配置完成后启用）
        self.start_browser_btn = ttk.Button(button_frame, text="启动浏览器", 
                                           command=self.start_web_automation,
                                           state=tk.DISABLED)
        self.start_browser_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 开始抓取按钮
        self.start_extraction_btn = ttk.Button(button_frame, text="开始抓取数据", 
                                              command=self.start_data_extraction, 
                                              state=tk.DISABLED)
        self.start_extraction_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 关闭浏览器按钮
        self.close_browser_btn = ttk.Button(button_frame, text="关闭浏览器", 
                                           command=self.close_web_automation,
                                           state=tk.DISABLED)
        self.close_browser_btn.pack(side=tk.LEFT)
        
        # 状态显示
        self.web_status_label = ttk.Label(web_automation_frame, 
                                         text="状态: 准备就绪", 
                                         foreground="green")
        self.web_status_label.pack(pady=(10, 0))
        
        return web_automation_frame
        
    def setup_web_result_table(self, parent):
        """设置网页数据获取结果表格"""
        # 网页数据表格
        web_columns = ['股票代码', '股票名称', '小草竞王', '小草红盘起爆', '小草绿盘低吸', '小草连板接力']
        self.web_result_tree = ttk.Treeview(parent, columns=web_columns, show='headings', height=10)
        
        # 设置列标题和宽度
        for col in web_columns:
            self.web_result_tree.heading(col, text=col)
            self.web_result_tree.column(col, width=100, anchor=tk.CENTER)
        
        # 添加滚动条
        web_scrollbar_v = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.web_result_tree.yview)
        web_scrollbar_h = ttk.Scrollbar(parent, orient=tk.HORIZONTAL, command=self.web_result_tree.xview)
        self.web_result_tree.configure(yscrollcommand=web_scrollbar_v.set, xscrollcommand=web_scrollbar_h.set)
        
        # 布局
        self.web_result_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 2))
        web_scrollbar_v.grid(row=0, column=1, sticky=(tk.N, tk.S))
        web_scrollbar_h.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # 配置权重
        parent.grid_rowconfigure(0, weight=1)
        parent.grid_columnconfigure(0, weight=1)
        
    def setup_result_table(self, parent):
        """设置结果表格"""
        # 结果表格 - 优化默认显示行数和列宽
        columns = GUI_CONFIG['table']['columns']
        self.result_tree = ttk.Treeview(parent, columns=columns, show='headings', height=10)
        
        # 设置列标题和宽度
        for i, col in enumerate(columns):
            self.result_tree.heading(col, text=col)
            width = GUI_CONFIG['table']['column_widths'][i]
            self.result_tree.column(col, width=width, anchor=tk.CENTER)
        
        # 添加滚动条
        scrollbar_v = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.result_tree.yview)
        scrollbar_h = ttk.Scrollbar(parent, orient=tk.HORIZONTAL, command=self.result_tree.xview)
        self.result_tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
        
        # 优化布局
        self.result_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 2))
        scrollbar_v.grid(row=0, column=1, sticky=(tk.N, tk.S))
        scrollbar_h.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # 配置权重
        parent.grid_rowconfigure(0, weight=1)
        parent.grid_columnconfigure(0, weight=1)
        
    def setup_layout(self):
        """设置布局权重"""
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)
        
        # 主框架布局权重
        main_frame = self.root.grid_slaves()[0]
        main_frame.grid_rowconfigure(0, weight=1)  # 标签页可扩展
        main_frame.grid_columnconfigure(0, weight=1)  # 单列可扩展
    
    def load_system_config(self):
        """从配置文件加载系统设置并更新勾选框状态"""
        try:
            # 读取鼠标定位功能配置
            self.enable_mouse_positioning_var.set(APP_CONFIG.get('enable_mouse_positioning', False))
            
            # 读取自动停止信号检测配置（使用买入信号配置作为基准）
            from config import SIGNAL_CONFIG
            self.auto_stop_signal_var.set(SIGNAL_CONFIG.get('auto_stop_at_market_close', True))
            
        except Exception as e:
            self.logger.error(f"加载系统配置失败: {str(e)}")
    
    def on_mouse_positioning_changed(self):
        """处理鼠标定位功能勾选框变化事件"""
        try:
            enabled = self.enable_mouse_positioning_var.get()
            
            # 调用配置更新函数
            from config import update_mouse_positioning_config
            success = update_mouse_positioning_config(enabled)
            
            if success:
                status_text = "已启用" if enabled else "已禁用"
                self.logger.info(f"鼠标定位功能{status_text}")
            else:
                self.logger.error("更新鼠标定位功能配置失败")
                # 如果保存失败，恢复勾选框状态
                self.enable_mouse_positioning_var.set(not enabled)
                
        except Exception as e:
            self.logger.error(f"处理鼠标定位功能变化事件失败: {str(e)}")
            # 发生异常时恢复勾选框状态
            enabled = self.enable_mouse_positioning_var.get()
            self.enable_mouse_positioning_var.set(not enabled)
    
    def on_auto_stop_changed(self):
        """处理自动停止信号检测勾选框变化事件"""
        try:
            enabled = self.auto_stop_signal_var.get()
            
            # 调用配置更新函数
            from config import update_auto_stop_config
            success = update_auto_stop_config(enabled)
            
            if success:
                status_text = "已启用" if enabled else "已禁用"
                self.logger.info(f"15:01自动停止信号检测功能{status_text}")
            else:
                self.logger.error("更新自动停止信号检测配置失败")
                # 如果保存失败，恢复勾选框状态
                self.auto_stop_signal_var.set(not enabled)
                
        except Exception as e:
            self.logger.error(f"处理自动停止信号检测变化事件失败: {str(e)}")
            # 发生异常时恢复勾选框状态
            enabled = self.auto_stop_signal_var.get()
            self.auto_stop_signal_var.set(not enabled)